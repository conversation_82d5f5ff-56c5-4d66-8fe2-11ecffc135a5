#!/usr/bin/env python3
"""
Test Script for Shruti <PERSON>le Manager Information
Specifically checks <PERSON><PERSON><PERSON>'s manager from Google Sheets
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
import requests
import csv
from io import String<PERSON>
from datetime import datetime
from src.manager_mapping import (
    DynamicManagerMapping, 
    get_manager_name, 
    get_manager_email,
    normalize_name,
    refresh_manager_mapping,
    MANAGER_EMAILS
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_google_sheets_raw_data():
    """Test direct connection to Google Sheets and show raw data"""
    print("🔍 TESTING DIRECT GOOGLE SHEETS ACCESS")
    print("=" * 50)
    
    try:
        # Direct Google Sheets access
        spreadsheet_id = "1hqj2whB7bH0aoDeNV-ORIl_5dXX0eHcglhabW9xeVt8"
        csv_url = f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}/export?format=csv"
        
        headers = {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        print(f"📡 Fetching data from Google Sheets...")
        print(f"🔗 URL: {csv_url}")
        
        response = requests.get(csv_url, timeout=30, headers=headers)
        
        if response.status_code == 200:
            print(f"✅ Successfully connected to Google Sheets")
            print(f"📊 Response size: {len(response.text)} characters")
            
            # Parse CSV data
            content = response.text
            csv_data = StringIO(content)
            reader = csv.reader(csv_data)
            
            # Show raw data structure
            rows = list(reader)
            print(f"📋 Total rows: {len(rows)}")
            
            if rows:
                print(f"\n📝 Header row: {rows[0]}")
                print(f"📝 Sample data rows:")
                for i, row in enumerate(rows[1:6]):  # Show first 5 data rows
                    print(f"  Row {i+1}: {row}")
            
            return True, rows
        else:
            print(f"❌ Failed to fetch data: HTTP {response.status_code}")
            return False, []
            
    except Exception as e:
        print(f"❌ Google Sheets connection failed: {e}")
        return False, []

def search_shruti_in_raw_data(rows):
    """Search for Shruti in raw Google Sheets data"""
    print("\n🔍 SEARCHING FOR SHRUTI IN RAW GOOGLE SHEETS DATA")
    print("=" * 50)
    
    if not rows:
        print("❌ No data to search")
        return
    
    # Skip header row
    data_rows = rows[1:] if len(rows) > 1 else []
    
    print(f"🔎 Searching through {len(data_rows)} data rows...")
    
    shruti_matches = []
    kamle_matches = []
    kamble_matches = []
    all_names = []
    
    for i, row in enumerate(data_rows, 1):
        if len(row) > 0 and row[0].strip():  # First column should be employee name
            employee_name = row[0].strip()
            all_names.append(employee_name)
            
            # Check for Shruti variations
            name_lower = employee_name.lower()
            
            if 'shruti' in name_lower:
                shruti_matches.append((i, row))
            
            if 'kamle' in name_lower:
                kamle_matches.append((i, row))
                
            if 'kamble' in name_lower:
                kamble_matches.append((i, row))
    
    print(f"\n📊 Search Results:")
    print(f"  Total employees found: {len(all_names)}")
    print(f"  Rows containing 'Shruti': {len(shruti_matches)}")
    print(f"  Rows containing 'Kamle': {len(kamle_matches)}")
    print(f"  Rows containing 'Kamble': {len(kamble_matches)}")
    
    if shruti_matches:
        print(f"\n✅ Found rows with 'Shruti':")
        for row_num, row in shruti_matches:
            employee = row[0] if len(row) > 0 else "N/A"
            manager = row[2] if len(row) > 2 else "N/A"
            manager_email = row[3] if len(row) > 3 else "N/A"
            print(f"  Row {row_num}: {employee} -> Manager: {manager}, Email: {manager_email}")
    
    if kamle_matches:
        print(f"\n✅ Found rows with 'Kamle':")
        for row_num, row in kamle_matches:
            employee = row[0] if len(row) > 0 else "N/A"
            manager = row[2] if len(row) > 2 else "N/A"
            manager_email = row[3] if len(row) > 3 else "N/A"
            print(f"  Row {row_num}: {employee} -> Manager: {manager}, Email: {manager_email}")
    
    if kamble_matches:
        print(f"\n✅ Found rows with 'Kamble':")
        for row_num, row in kamble_matches:
            employee = row[0] if len(row) > 0 else "N/A"
            manager = row[2] if len(row) > 2 else "N/A"
            manager_email = row[3] if len(row) > 3 else "N/A"
            print(f"  Row {row_num}: {employee} -> Manager: {manager}, Email: {manager_email}")
    
    if not shruti_matches and not kamle_matches and not kamble_matches:
        print(f"\n❌ No matches found for 'Shruti', 'Kamle', or 'Kamble'")
        
        # Show similar names
        print(f"\n📋 Looking for similar names...")
        similar_names = []
        for name in all_names:
            name_lower = name.lower()
            if any(part in name_lower for part in ['shr', 'kam', 'sru']):
                similar_names.append(name)
        
        if similar_names:
            print(f"Found {len(similar_names)} names with similar patterns:")
            for name in similar_names[:10]:  # Show first 10
                print(f"  - {name}")
            if len(similar_names) > 10:
                print(f"  ... and {len(similar_names) - 10} more")
        
        # Show first 20 employee names for reference
        print(f"\n📋 First 20 employee names in sheet:")
        for i, name in enumerate(all_names[:20], 1):
            print(f"  {i:2d}. {name}")
        
        if len(all_names) > 20:
            print(f"  ... and {len(all_names) - 20} more employees")

def test_shruti_manager_lookup():
    """Test specific lookup for Shruti Kamle/Kamble using the mapping system"""
    print("\n🔍 TESTING SHRUTI KAMLE MANAGER LOOKUP THROUGH MAPPING SYSTEM")
    print("=" * 50)
    
    # Test different name variations
    name_variations = [
        'Shruti Kamle',
        'Shruti Kamble', 
        'shruti kamle',
        'shruti kamble',
        'Shruti',
        'SHRUTI KAMLE',
        'SHRUTI KAMBLE',
        'Shruti K',
        'Shruti Kamle.',
        'Shruti Kamble.'
    ]
    
    print("Testing different name variations through the mapping system:")
    
    for name in name_variations:
        print(f"\n🔎 Testing name: '{name}'")
        
        # Get manager name (force refresh to get latest data)
        manager_name = get_manager_name(name, force_refresh=True)
        print(f"  Manager Name: {manager_name if manager_name else 'NOT FOUND'}")
        
        # Get manager email
        manager_email = get_manager_email(name, force_refresh=True)
        print(f"  Manager Email: {manager_email if manager_email else 'NOT FOUND'}")
        
        # Test normalization
        normalized = normalize_name(name)
        print(f"  Normalized Name: {normalized}")
        
        if manager_name:
            # Check if manager email is configured
            if manager_name in MANAGER_EMAILS:
                print(f"  ✅ Manager email configured: {MANAGER_EMAILS[manager_name]}")
            else:
                print(f"  ⚠️  Manager email not configured for: {manager_name}")

def test_current_mapping_data():
    """Test current mapping data from Google Sheets"""
    print("\n🔍 TESTING CURRENT MAPPING DATA")
    print("=" * 50)
    
    try:
        # Force refresh from Google Sheets
        print("🔄 Force refreshing data from Google Sheets...")
        mapping = refresh_manager_mapping()
        
        print(f"📊 Total employees in mapping: {len(mapping)}")
        
        # Look for Shruti specifically
        shruti_entries = []
        for employee, manager in mapping.items():
            if 'shruti' in employee.lower():
                shruti_entries.append((employee, manager))
        
        if shruti_entries:
            print(f"\n✅ Found {len(shruti_entries)} Shruti entries:")
            for employee, manager in shruti_entries:
                print(f"  📝 {employee} -> {manager}")
                
                # Check manager email status
                if manager in MANAGER_EMAILS:
                    print(f"      📧 Manager Email: {MANAGER_EMAILS[manager]}")
                else:
                    print(f"      ⚠️  Manager Email: NOT CONFIGURED")
        else:
            print("\n❌ No Shruti entries found in current mapping")
            
            # Show employees with similar names
            similar_names = []
            for employee in mapping.keys():
                if any(part in employee.lower() for part in ['shr', 'kam']):
                    similar_names.append(employee)
            
            if similar_names:
                print(f"\n📋 Similar names found:")
                for name in similar_names:
                    print(f"  📝 {name} -> {mapping[name]}")
        
        return mapping
        
    except Exception as e:
        print(f"❌ Failed to get current mapping: {e}")
        return {}

def main():
    """Run all tests for Shruti Kamle manager information"""
    print("🚀 SHRUTI KAMLE MANAGER INFORMATION TEST")
    print("=" * 60)
    print("Testing Google Sheets integration for Shruti Kamle's manager information")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: Direct Google Sheets Connection and Raw Data Search
    success, raw_data = test_google_sheets_raw_data()
    
    if success and raw_data:
        search_shruti_in_raw_data(raw_data)
    
    # Test 2: Shruti-specific lookup through mapping system
    test_shruti_manager_lookup()
    
    # Test 3: Current mapping data
    current_mapping = test_current_mapping_data()
    
    # Final Summary
    print("\n" + "=" * 60)
    print("📋 FINAL SUMMARY FOR SHRUTI KAMLE")
    print("=" * 60)
    
    if current_mapping:
        # Final comprehensive check
        shruti_found = False
        print("🔍 Final comprehensive search results:")
        
        for employee, manager in current_mapping.items():
            if 'shruti' in employee.lower():
                print(f"✅ FOUND: {employee} -> Manager: {manager}")
                
                # Get manager email status
                manager_email = get_manager_email(employee)
                if manager_email:
                    print(f"📧 Manager Email: {manager_email}")
                else:
                    print(f"⚠️  Manager Email: NOT AVAILABLE")
                    if manager in MANAGER_EMAILS:
                        print(f"   (Email configured but may be inactive: {MANAGER_EMAILS[manager]})")
                    else:
                        print(f"   (No email configured for manager: {manager})")
                
                shruti_found = True
        
        if not shruti_found:
            print("❌ RESULT: Shruti Kamle/Kamble not found in current Google Sheets data")
            print("\n💡 Possible reasons:")
            print("   1. Name spelling is different in the Google Sheet")
            print("   2. Employee is not listed in the current sheet")
            print("   3. Sheet structure or format has changed")
            print("   4. Employee may have been removed from the sheet")
    
    print(f"\n🔗 Google Sheet URL:")
    print("https://docs.google.com/spreadsheets/d/1hqj2whB7bH0aoDeNV-ORIl_5dXX0eHcglhabW9xeVt8/edit?usp=sharing")
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
