#!/usr/bin/env python3
"""
Test the fix for Shruti Kamle name normalization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.manager_mapping import (
    get_manager_name, 
    get_manager_email,
    normalize_name,
    refresh_manager_mapping
)

def test_shruti_fix():
    """Test that <PERSON>hr<PERSON> now correctly maps to Shr<PERSON> and <PERSON>"""
    print("🔧 TESTING SHRUTI KAMLE NAME NORMALIZATION FIX")
    print("=" * 60)
    
    # Force refresh to get latest data
    print("🔄 Refreshing manager mapping from Google Sheets...")
    refresh_manager_mapping()
    
    # Test different variations of Shruti Kamle
    test_names = [
        'Shruti Kamle',
        'shruti kamle', 
        'SHRUTI KAMLE',
        'Shruti Kamble',  # Correct spelling for comparison
        'shruti kamble',
        'SHRUTI KAMBLE'
    ]
    
    print("\n🔍 Testing name variations:")
    print("-" * 60)
    
    for name in test_names:
        print(f"\n🔎 Testing: '{name}'")
        
        # Test normalization
        normalized = normalize_name(name)
        print(f"  ✅ Normalized to: '{normalized}'")
        
        # Test manager lookup
        manager = get_manager_name(name, force_refresh=False)  # Use cached data
        print(f"  👔 Manager: {manager}")
        
        # Test email lookup
        email = get_manager_email(name, force_refresh=False)  # Use cached data
        print(f"  📧 Manager Email: {email}")
        
        # Check if this is the expected result
        if 'kamle' in name.lower():
            if normalized == 'Shruti Kamble' and manager == 'David Lawler':
                print(f"  ✅ CORRECT: Kamle correctly mapped to Kamble -> David Lawler")
            else:
                print(f"  ❌ INCORRECT: Expected Shruti Kamble -> David Lawler")
        elif 'kamble' in name.lower():
            if manager == 'David Lawler':
                print(f"  ✅ CORRECT: Kamble correctly mapped to David Lawler")
            else:
                print(f"  ❌ INCORRECT: Expected David Lawler for Kamble")
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    # Final verification
    kamle_manager = get_manager_name('Shruti Kamle')
    kamle_email = get_manager_email('Shruti Kamle')
    
    kamble_manager = get_manager_name('Shruti Kamble')
    kamble_email = get_manager_email('Shruti Kamble')
    
    print(f"Final Results:")
    print(f"  'Shruti Kamle'  -> Manager: {kamle_manager}, Email: {kamle_email}")
    print(f"  'Shruti Kamble' -> Manager: {kamble_manager}, Email: {kamble_email}")
    
    if kamle_manager == kamble_manager == 'David Lawler':
        print(f"\n🎉 SUCCESS: Fix is working correctly!")
        print(f"   Both 'Kamle' and 'Kamble' now correctly map to David Lawler")
    else:
        print(f"\n❌ ISSUE: Fix may not be working as expected")
        print(f"   Expected both to map to 'David Lawler'")
    
    return kamle_manager == 'David Lawler'

if __name__ == "__main__":
    success = test_shruti_fix()
    if success:
        print(f"\n✅ Test PASSED: Shruti Kamle normalization fix is working!")
    else:
        print(f"\n❌ Test FAILED: Fix needs more work")
