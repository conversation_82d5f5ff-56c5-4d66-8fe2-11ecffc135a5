#!/usr/bin/env python3
"""
Check <PERSON><PERSON><PERSON>'s manager in the system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.manager_mapping import (
    get_manager_name, 
    get_manager_email,
    normalize_name,
    refresh_manager_mapping,
    get_employees_by_manager
)

def check_shruti_manager():
    """Check <PERSON><PERSON><PERSON>'s manager information"""
    print("🔍 CHECKING SHRUTI KAMLE'S MANAGER")
    print("=" * 60)
    
    # Force refresh to get latest data
    print("🔄 Refreshing manager mapping from Google Sheets...")
    refresh_manager_mapping()
    
    # Check <PERSON><PERSON><PERSON>'s manager
    name = "<PERSON><PERSON><PERSON>"
    normalized = normalize_name(name)
    manager = get_manager_name(name)
    manager_email = get_manager_email(name)
    
    print(f"\n📋 Results:")
    print(f"  Employee: {name}")
    print(f"  Normalized Name: {normalized}")
    print(f"  Manager: {manager}")
    print(f"  Manager Email: {manager_email}")
    
    # Check who else reports to the same manager
    if manager:
        team_members = get_employees_by_manager(manager)
        print(f"\n👥 Other employees reporting to {manager}:")
        for employee in team_members:
            if employee != normalized:  # Skip Shruti herself
                print(f"  - {employee}")
    
    print("\n" + "=" * 60)
    return manager

if __name__ == "__main__":
    manager = check_shruti_manager()
    print(f"✅ Shruti Kamle's manager is: {manager}")
