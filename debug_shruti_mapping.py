#!/usr/bin/env python3
"""
Debug Shruti Kamble Manager Mapping
Check why there's a discrepancy in manager assignment
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import logging
import requests
import csv
from io import String<PERSON>
from datetime import datetime
from src.manager_mapping import (
    DynamicManagerMapping, 
    get_manager_name, 
    get_manager_email,
    normalize_name,
    refresh_manager_mapping,
    MANAGER_EMAILS
)

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def debug_google_sheets_data():
    """Debug the exact Google Sheets data"""
    print("🔍 DEBUGGING GOOGLE SHEETS DATA FOR SHRUTI")
    print("=" * 50)
    
    try:
        # Direct Google Sheets access
        spreadsheet_id = "1hqj2whB7bH0aoDeNV-ORIl_5dXX0eHcglhabW9xeVt8"
        csv_url = f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}/export?format=csv"
        
        headers = {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        print(f"📡 Fetching fresh data from Google Sheets...")
        response = requests.get(csv_url, timeout=30, headers=headers)
        
        if response.status_code == 200:
            content = response.text
            csv_data = StringIO(content)
            reader = csv.reader(csv_data)
            
            rows = list(reader)
            print(f"📋 Total rows: {len(rows)}")
            
            if rows:
                header = rows[0]
                print(f"📝 Header: {header}")
                
                # Find all Shruti entries
                shruti_entries = []
                for i, row in enumerate(rows[1:], 1):
                    if len(row) > 0 and 'shruti' in row[0].lower():
                        shruti_entries.append((i, row))
                
                print(f"\n✅ Found {len(shruti_entries)} Shruti entries:")
                for row_num, row in shruti_entries:
                    name = row[0] if len(row) > 0 else "N/A"
                    email = row[1] if len(row) > 1 else "N/A"
                    manager = row[2] if len(row) > 2 else "N/A"
                    manager_email = row[3] if len(row) > 3 else "N/A"
                    
                    print(f"  Row {row_num}:")
                    print(f"    Name: {name}")
                    print(f"    Email: {email}")
                    print(f"    Manager: {manager}")
                    print(f"    Manager Email: {manager_email}")
                    print()
                
                return shruti_entries
            
        else:
            print(f"❌ Failed to fetch data: HTTP {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def debug_mapping_system():
    """Debug the mapping system processing"""
    print("🔍 DEBUGGING MAPPING SYSTEM PROCESSING")
    print("=" * 50)
    
    # Create fresh mapping instance
    mapping_instance = DynamicManagerMapping()
    
    # Force fresh fetch
    print("🔄 Force fetching fresh mapping...")
    fresh_mapping = mapping_instance._fetch_manager_mapping_from_sheets()
    
    if fresh_mapping:
        print(f"📊 Total mappings: {len(fresh_mapping)}")
        
        # Find all Shruti entries in the processed mapping
        shruti_mappings = {}
        for employee, manager in fresh_mapping.items():
            if 'shruti' in employee.lower():
                shruti_mappings[employee] = manager
        
        print(f"\n✅ Shruti entries in processed mapping:")
        for employee, manager in shruti_mappings.items():
            print(f"  {employee} -> {manager}")
            
            # Check manager email
            if manager in MANAGER_EMAILS:
                print(f"    Manager Email: {MANAGER_EMAILS[manager]}")
            else:
                print(f"    Manager Email: NOT CONFIGURED")
        
        return shruti_mappings
    else:
        print("❌ Failed to get fresh mapping")
        return {}

def debug_name_normalization():
    """Debug name normalization for Shruti variations"""
    print("\n🔍 DEBUGGING NAME NORMALIZATION")
    print("=" * 50)
    
    test_names = [
        'Shruti Kamle',
        'Shruti Kamble',
        'shruti kamle',
        'shruti kamble',
        'SHRUTI KAMLE',
        'SHRUTI KAMBLE'
    ]
    
    for name in test_names:
        print(f"\n🔎 Testing: '{name}'")
        
        # Test normalization
        normalized = normalize_name(name)
        print(f"  Normalized to: '{normalized}'")
        
        # Test manager lookup
        manager = get_manager_name(name, force_refresh=True)
        print(f"  Manager found: {manager}")
        
        # Test email lookup
        email = get_manager_email(name, force_refresh=True)
        print(f"  Manager email: {email}")

def debug_exact_lookup():
    """Debug exact lookup for specific names"""
    print("\n🔍 DEBUGGING EXACT LOOKUP")
    print("=" * 50)
    
    # Get current mapping
    mapping = refresh_manager_mapping()
    
    print("🔎 Searching for exact matches:")
    
    # Look for exact name matches
    exact_matches = []
    partial_matches = []
    
    for employee in mapping.keys():
        employee_lower = employee.lower()
        
        if employee_lower == 'shruti kamle':
            exact_matches.append(('shruti kamle', employee, mapping[employee]))
        elif employee_lower == 'shruti kamble':
            exact_matches.append(('shruti kamble', employee, mapping[employee]))
        elif 'shruti' in employee_lower and ('kamle' in employee_lower or 'kamble' in employee_lower):
            partial_matches.append((employee, mapping[employee]))
    
    if exact_matches:
        print(f"\n✅ Exact matches found:")
        for search_term, actual_name, manager in exact_matches:
            print(f"  Searched: '{search_term}' -> Found: '{actual_name}' -> Manager: {manager}")
    
    if partial_matches:
        print(f"\n✅ Partial matches found:")
        for employee, manager in partial_matches:
            print(f"  '{employee}' -> Manager: {manager}")
    
    if not exact_matches and not partial_matches:
        print(f"\n❌ No matches found")
        
        # Show all employees with 'shruti' in name
        all_shruti = [(emp, mgr) for emp, mgr in mapping.items() if 'shruti' in emp.lower()]
        if all_shruti:
            print(f"\n📋 All Shruti entries:")
            for emp, mgr in all_shruti:
                print(f"  '{emp}' -> {mgr}")

def main():
    """Run debug analysis"""
    print("🚀 DEBUGGING SHRUTI KAMBLE MANAGER MAPPING")
    print("=" * 60)
    print(f"Debug started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Step 1: Check raw Google Sheets data
    raw_shruti_data = debug_google_sheets_data()
    
    # Step 2: Check mapping system processing
    processed_mappings = debug_mapping_system()
    
    # Step 3: Debug name normalization
    debug_name_normalization()
    
    # Step 4: Debug exact lookup
    debug_exact_lookup()
    
    # Final Analysis
    print("\n" + "=" * 60)
    print("📋 FINAL ANALYSIS")
    print("=" * 60)
    
    print("🔍 Based on the debug analysis:")
    
    if raw_shruti_data:
        print(f"\n📊 Raw Google Sheets data shows:")
        for row_num, row in raw_shruti_data:
            name = row[0] if len(row) > 0 else "N/A"
            manager = row[2] if len(row) > 2 else "N/A"
            print(f"  {name} -> {manager}")
    
    if processed_mappings:
        print(f"\n🔄 Processed mapping system shows:")
        for employee, manager in processed_mappings.items():
            print(f"  {employee} -> {manager}")
    
    print(f"\n💡 The discrepancy might be due to:")
    print(f"  1. Name normalization logic")
    print(f"  2. Case sensitivity issues")
    print(f"  3. Recent updates to the Google Sheet")
    print(f"  4. Caching issues in the mapping system")
    
    print(f"\nDebug completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
